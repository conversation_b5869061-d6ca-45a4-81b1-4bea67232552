'use client'

import { useMemo } from 'react'
import Link from 'next/link'
import { format } from 'date-fns'
import {
    Avatar,
    AvatarFallback,
    Badge,
    Button,
    getCrewInitials,
    P,
    Popover,
    PopoverContent,
    PopoverTrigger,
    Tooltip,
    TooltipContent,
    TooltipTrigger,
} from '@/components/ui'
import { Label } from '@/components/ui/label'
import { LocationModal } from '../vessels/list'
import { cn } from '@/app/lib/utils'
import {
    createColumns,
    DataTable,
    ExtendedColumnDef,
} from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { FilteredTable } from '@/components/filteredTable'
import { mergeAndSortCrewTrainingData, UnifiedTrainingData } from '@/app/lib/crew-training-utils'

// Helper function to format dates using date-fns
const formatDate = (dateString: any) => {
    if (!dateString) return ''
    try {
        const date = new Date(dateString)
        return format(date, 'dd/MM/yy')
    } catch {
        return ''
    }
}

// Row status evaluator for highlighting overdue/upcoming rows
type RowStatus = 'overdue' | 'upcoming' | 'normal'

const getRowStatus = (rowData: UnifiedTrainingData): RowStatus => {
    if (rowData.status.isOverdue) {
        return 'overdue'
    }
    if (rowData.status.dueWithinSevenDays) {
        return 'upcoming'
    }
    return 'normal'
}

// Mobile card component for unified training data
interface UnifiedMobileTrainingCardProps {
    data: UnifiedTrainingData
    memberId?: number
}

const UnifiedMobileTrainingCard = ({
    data,
    memberId,
}: UnifiedMobileTrainingCardProps) => {
    const isCompleted = data.category === 'completed'
    const isOverdue = data.category === 'overdue'
    const members = data.members || []
    const trainingTitle = data.trainingType?.title || ''

    return (
        <div className="w-full space-y-3 tablet-md:border-none border-b border-border py-3 small:pe-4">
            <div className="flex flex-wrap justify-between items-center">
                {isCompleted ? (
                    <Link
                        href={`/crew-training/info?id=${data.id}`}
                        className="font-semibold text-base hover:text-primary">
                        {formatDate(data.dueDate)}
                    </Link>
                ) : (
                    <div className={cn(
                        "font-semibold text-base",
                        isOverdue && "text-cinnabar-500 hover:text-cinnabar-700"
                    )}>
                        {trainingTitle}
                    </div>
                )}
                <div className="flex gap-2 items-center landscape:hidden">
                    <Label className="text-sm m-0 text-muted-foreground">
                        Status:
                    </Label>
                    <div
                        className={cn(
                            "text-sm font-medium px-2 py-1 rounded-md",
                            data.status.isOverdue
                                ? 'bg-destructive/10 text-destructive'
                                : data.status.dueWithinSevenDays
                                ? 'bg-warning/10 text-warning'
                                : 'bg-muted/50 text-muted-foreground'
                        )}>
                        {data.status.label || data.category}
                    </div>
                </div>
            </div>

            <div className="tablet-md:hidden space-y-[7px]">
                <Label className="text-sm m-0 text-muted-foreground">
                    {isCompleted ? 'Training Details:' : 'Due Date:'}
                </Label>
                <div className={cn(
                    "text-sm",
                    isOverdue && "text-cinnabar-500"
                )}>
                    {isCompleted
                        ? trainingTitle
                        : data.dueDate
                          ? formatDate(data.dueDate)
                          : 'Not specified'}
                </div>
            </div>

            <Label
                position="left"
                className="text-sm laptop:hidden text-muted-foreground"
                label="Crew Members:">
                <div className="flex gap-1">
                    {members.slice(0, 6).map((member: any) => (
                        <Tooltip key={member.id}>
                            <TooltipTrigger>
                                <Avatar
                                    size="sm"
                                    variant={
                                        isOverdue ? 'destructive' : 'secondary'
                                    }>
                                    <AvatarFallback className="text-sm">
                                        {getCrewInitials(
                                            member.firstName,
                                            member.surname,
                                        )}
                                    </AvatarFallback>
                                </Avatar>
                            </TooltipTrigger>
                            <TooltipContent>
                                {member.firstName} {member.surname ?? ''}
                            </TooltipContent>
                        </Tooltip>
                    ))}
                    {members.length > 6 && (
                        <Popover>
                            <PopoverTrigger className="w-fit" asChild>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="w-fit">
                                    +{members.length - 6} more
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent>
                                <div className="p-3 w-64 max-h-64 overflow-auto">
                                    <div className="space-y-2">
                                        {members
                                            .slice(6)
                                            .map((remainingMember: any) => (
                                                <div
                                                    key={remainingMember.id}
                                                    className="text-sm">
                                                    {`${remainingMember.firstName ?? ''} ${remainingMember.surname ?? ''}`}
                                                </div>
                                            ))}
                                    </div>
                                </div>
                            </PopoverContent>
                        </Popover>
                    )}
                </div>
            </Label>

            <div className="flex justify-between landscape:hidden items-center">
                <Label className="text-sm m-0 text-muted-foreground">
                    Vessel:
                </Label>
                <div className="flex items-center gap-2">
                    <span className="text-sm text-nowrap">
                        {data.vessel?.title || ''}
                    </span>
                    <LocationModal
                        vessel={data.vessel}
                        iconClassName="size-8"
                    />
                </div>
            </div>
        </div>
    )
}

// Main unified training table component
interface UnifiedTrainingTableProps {
    trainingSessionDues?: any[]
    completedTrainingList?: any[]
    getVesselWithIcon?: (id: any, vessel: any) => any
    includeCompleted?: boolean
    memberId?: number
    isVesselView?: boolean
    showToolbar?: boolean
    pageSize?: number
}

export const UnifiedTrainingTable = ({
    trainingSessionDues = [],
    completedTrainingList = [],
    getVesselWithIcon,
    includeCompleted = true,
    memberId,
    isVesselView = false,
    showToolbar = false,
    pageSize,
}: UnifiedTrainingTableProps) => {
    // Use the utility function to merge and sort data
    const unifiedData = useMemo(() => {
        return mergeAndSortCrewTrainingData({
            trainingSessionDues,
            completedTrainingList,
            getVesselWithIcon,
            includeCompleted,
        })
    }, [trainingSessionDues, completedTrainingList, getVesselWithIcon, includeCompleted])

    // Create table columns
    const columns = useMemo(() => createColumns([
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Training" />
            ),
            cell: ({ row }: { row: any }) => {
                const training: UnifiedTrainingData = row.original
                return (
                    <UnifiedMobileTrainingCard
                        data={training}
                        memberId={memberId}
                    />
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                // Sort by category priority first, then by date
                const trainingA: UnifiedTrainingData = rowA.original
                const trainingB: UnifiedTrainingData = rowB.original
                
                const priorityA = trainingA.category === 'overdue' ? 1 : trainingA.category === 'upcoming' ? 2 : 3
                const priorityB = trainingB.category === 'overdue' ? 1 : trainingB.category === 'upcoming' ? 2 : 3
                
                if (priorityA !== priorityB) {
                    return priorityA - priorityB
                }
                
                const dateA = new Date(trainingA.dueDate).getTime()
                const dateB = new Date(trainingB.dueDate).getTime()
                
                return trainingA.category === 'completed' ? dateB - dateA : dateA - dateB
            },
        },
        {
            accessorKey: 'trainingType',
            cellAlignment: 'left',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Training Type" />
            ),
            breakpoint: 'tablet-md',
            cell: ({ row }: { row: any }) => {
                const training: UnifiedTrainingData = row.original
                return (
                    <P className={cn(
                        training.status.isOverdue && "text-cinnabar-500 hover:text-cinnabar-700"
                    )}>
                        {training.trainingType?.title || ''}
                    </P>
                )
            },
        },
        {
            accessorKey: 'dueDate',
            cellAlignment: 'right',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Date" />
            ),
            breakpoint: 'landscape',
            cell: ({ row }: { row: any }) => {
                const training: UnifiedTrainingData = row.original
                return (
                    <div className={cn(
                        "text-right",
                        training.status.isOverdue && "text-cinnabar-500 hover:text-cinnabar-700"
                    )}>
                        {formatDate(training.dueDate)}
                    </div>
                )
            },
        },
        {
            accessorKey: 'vessel',
            cellAlignment: 'left',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Vessel" />
            ),
            breakpoint: 'landscape',
            cell: ({ row }: { row: any }) => {
                const training: UnifiedTrainingData = row.original
                return (
                    <div className="flex items-center gap-2">
                        <span className="text-sm text-nowrap">
                            {training.vessel?.title || ''}
                        </span>
                        <LocationModal
                            vessel={training.vessel}
                            iconClassName="size-8"
                        />
                    </div>
                )
            },
        },
    ]), [memberId])

    if (!unifiedData?.length) {
        return (
            <div className="text-center py-8 text-muted-foreground">
                No training data available
            </div>
        )
    }

    return (
        <FilteredTable
            columns={columns}
            data={unifiedData}
            showToolbar={showToolbar}
            rowStatus={getRowStatus}
            pageSize={pageSize || unifiedData.length}
        />
    )
}

export default UnifiedTrainingTable
