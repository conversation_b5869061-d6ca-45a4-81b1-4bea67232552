"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/edit/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/type-multiselect-dropdown.tsx":
/*!****************************************************************!*\
  !*** ./src/app/ui/crew-training/type-multiselect-dropdown.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_offline_models_trainingType__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/offline/models/trainingType */ \"(app-pages-browser)/./src/app/offline/models/trainingType.js\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst TrainingTypeMultiSelectDropdown = (param)=>{\n    let { value = [], onChange, locked, offline = false } = param;\n    _s();\n    const [typesOfTraining, setTypesOfTraining] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTrainings, setSelectedTrainings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const limit = 100 // Default limit from the query\n    ;\n    const [currentOffset, setCurrentOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [hasMorePages, setHasMorePages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingMore, setIsLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const trainingTypeModel = new _app_offline_models_trainingType__WEBPACK_IMPORTED_MODULE_3__[\"default\"]();\n    const handleSetTrainingTypes = (data)=>{\n        const formattedData = data.map((trainingType)=>({\n                value: trainingType.id,\n                label: trainingType.title\n            }));\n        formattedData.sort((a, b)=>a.label.localeCompare(b.label));\n        setTypesOfTraining(formattedData);\n        const selectedData = value.map((value)=>{\n            return formattedData.find((type)=>type.value === value);\n        });\n        setSelectedTrainings(selectedData);\n    };\n    const [queryTrainingTypes] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_6__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CREW_TRAINING_TYPES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingTypes.nodes;\n            const pageInfo = response.readTrainingTypes.pageInfo;\n            if (data) {\n                const formattedData = data.map((trainingType)=>({\n                        value: trainingType.id,\n                        label: trainingType.title\n                    }));\n                const updatedTrainingTypes = [\n                    ...typesOfTraining,\n                    ...formattedData\n                ];\n                setTypesOfTraining(updatedTrainingTypes);\n                if (pageInfo.hasNextPage) {\n                    // Prepare for next page\n                    const nextOffset = currentOffset + limit;\n                    setCurrentOffset(nextOffset);\n                    // Load next page\n                    setIsLoadingMore(true);\n                    queryTrainingTypes({\n                        variables: {\n                            limit: limit,\n                            offset: nextOffset\n                        }\n                    });\n                } else {\n                    setHasMorePages(false);\n                    setIsLoadingMore(false);\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingTypes error\", error);\n            setIsLoadingMore(false);\n        }\n    });\n    const loadTrainingTypes = async ()=>{\n        setTypesOfTraining([]);\n        setCurrentOffset(0);\n        setHasMorePages(true);\n        setIsLoadingMore(true);\n        await queryTrainingTypes({\n            variables: {\n                limit: limit,\n                offset: 0\n            }\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoadingMore) {\n            let formattedData = [\n                ...typesOfTraining\n            ];\n            formattedData.sort((a, b)=>a.label.localeCompare(b.label));\n            setTypesOfTraining(formattedData);\n            const selectedData = value.map((value)=>{\n                return formattedData.find((type)=>type.value === value);\n            });\n            setSelectedTrainings(selectedData);\n        }\n    }, [\n        isLoadingMore\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            trainingTypeModel.getAll().then((data)=>{\n                handleSetTrainingTypes(data);\n            });\n        }\n    }, [\n        offline\n    ]);\n    const handleOnChange = (value)=>{\n        // Handle case when all options are deselected (value is null or empty array)\n        if (!value || Array.isArray(value) && value.length === 0) {\n            setSelectedTrainings([]);\n            onChange([]);\n            return;\n        }\n        const selectedValues = Array.isArray(value) ? value : [\n            value\n        ];\n        setSelectedTrainings(selectedValues);\n        onChange(selectedValues);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(value) && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(typesOfTraining)) {\n            const selectedData = value.map((value)=>{\n                return typesOfTraining.find((type)=>type.value === value);\n            });\n            setSelectedTrainings(selectedData);\n        }\n    }, [\n        value,\n        typesOfTraining\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!offline) {\n            loadTrainingTypes();\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_4__.Combobox, {\n        disabled: locked,\n        value: selectedTrainings,\n        options: typesOfTraining,\n        onChange: handleOnChange,\n        isLoading: !typesOfTraining,\n        buttonClassName: \"w-full\",\n        labelClassName: \"w-full\",\n        placeholder: \"Select training type\",\n        multi: true\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\type-multiselect-dropdown.tsx\",\n        lineNumber: 139,\n        columnNumber: 9\n    }, undefined);\n};\n_s(TrainingTypeMultiSelectDropdown, \"57wkxefNIEgvwFK79REOZDkCI/I=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_6__.useLazyQuery\n    ];\n});\n_c = TrainingTypeMultiSelectDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TrainingTypeMultiSelectDropdown);\nvar _c;\n$RefreshReg$(_c, \"TrainingTypeMultiSelectDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/type-multiselect-dropdown.tsx\n"));

/***/ })

});