"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-example.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingExample: function() { return /* binding */ UnifiedTrainingExample; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/lib/crew-training-utils */ \"(app-pages-browser)/./src/app/lib/crew-training-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingExample,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst UnifiedTrainingExample = (param)=>{\n    let { vesselId, memberId, isVesselView = false } = param;\n    _s();\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [includeCompleted, setIncludeCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData)();\n    // Query for training session dues (overdue/upcoming)\n    const [queryTrainingSessionDues, { loading: duesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_10__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes || [];\n            setTrainingSessionDues(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n        }\n    });\n    // Query for completed training sessions\n    const [queryCompletedTraining, { loading: completedLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_10__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes || [];\n            setCompletedTrainingList(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading completed training:\", error);\n        }\n    });\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadData();\n    }, [\n        vesselId,\n        memberId\n    ]);\n    const loadData = async ()=>{\n        setLoading(true);\n        // Build filters based on props\n        const duesFilter = {};\n        const completedFilter = {};\n        if (vesselId) {\n            duesFilter.vesselID = {\n                eq: vesselId\n            };\n            completedFilter.vesselID = {\n                eq: vesselId\n            };\n        }\n        if (memberId) {\n            duesFilter.memberID = {\n                eq: memberId\n            };\n        // For completed training, we'd need to filter by members in the training session\n        }\n        // Load training session dues\n        await queryTrainingSessionDues({\n            variables: {\n                limit: 1000,\n                offset: 0,\n                filter: duesFilter\n            }\n        });\n        // Load completed training sessions\n        await queryCompletedTraining({\n            variables: {\n                limit: 1000,\n                offset: 0,\n                filter: completedFilter\n            }\n        });\n        setLoading(false);\n    };\n    // Calculate statistics using the utility function\n    const unifiedData = (0,_app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.mergeAndSortCrewTrainingData)({\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    });\n    const stats = (0,_app_lib_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.getTrainingDataStats)(unifiedData);\n    const isLoading = loading || duesLoading || completedLoading;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n        className: \"p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_6__.H2, {\n                                    children: \"Crew Training Overview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground mt-1\",\n                                    children: \"Unified view of all training activities\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"destructive\",\n                                    type: \"normal\",\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"w-2 h-2 bg-current rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        \"Overdue: \",\n                                        stats.overdue\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"secondary\",\n                                    type: \"normal\",\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"w-2 h-2 bg-yellow-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        \"Upcoming: \",\n                                        stats.upcoming\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"outline\",\n                                    type: \"normal\",\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        \"Completed: \",\n                                        stats.completed\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"outline\",\n                                    type: \"normal\",\n                                    children: [\n                                        \"Total: \",\n                                        stats.total\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            size: \"sm\",\n                            onClick: ()=>setIncludeCompleted(!includeCompleted),\n                            children: [\n                                includeCompleted ? \"Hide\" : \"Show\",\n                                \" Completed\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: loadData,\n                            disabled: isLoading,\n                            children: isLoading ? \"Loading...\" : \"Refresh\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border rounded-lg\",\n                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Loading training data...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 25\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                        trainingSessionDues: trainingSessionDues,\n                        completedTrainingList: completedTrainingList,\n                        getVesselWithIcon: getVesselWithIcon,\n                        includeCompleted: includeCompleted,\n                        memberId: memberId,\n                        isVesselView: isVesselView,\n                        showToolbar: false\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 17\n                }, undefined),\n                !isLoading && unifiedData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-muted-foreground\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Showing \",\n                            unifiedData.length,\n                            \" training record\",\n                            unifiedData.length !== 1 ? \"s\" : \"\",\n                            \". Data is sorted with overdue trainings first, then upcoming, then completed.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 21\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 120,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n        lineNumber: 119,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedTrainingExample, \"ePAYbdsryHpqfuqpI2nNewAa4TM=\", false, function() {\n    return [\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_10__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_10__.useLazyQuery\n    ];\n});\n_c = UnifiedTrainingExample;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingExample);\nvar _c;\n$RefreshReg$(_c, \"UnifiedTrainingExample\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx\n"));

/***/ })

});