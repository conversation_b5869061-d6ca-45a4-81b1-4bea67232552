import { GetTrainingSessionStatus } from './actions'

/**
 * Unified training data interface that combines overdue, upcoming, and completed training data
 */
export interface UnifiedTrainingData {
    id: number
    dueDate: string // For overdue/upcoming this is the due date, for completed this is the completion date
    vesselID: number
    vessel: {
        id: number
        title: string
        [key: string]: any // Allow for additional vessel properties like position, etc.
    }
    trainingTypeID: number
    trainingType: {
        id: number
        title: string
    }
    members: Array<{
        id: number
        firstName?: string
        surname?: string
    }>
    status: {
        class: string
        label: string
        isOverdue: boolean
        dueWithinSevenDays: boolean
    }
    category: 'overdue' | 'upcoming' | 'completed' // Added to help with sorting and display
    originalData?: any // Store original data for reference if needed
}

/**
 * Training priority levels for sorting
 */
export enum TrainingPriority {
    OVERDUE = 1,
    UPCOMING = 2,
    COMPLETED = 3
}

/**
 * Transform completed training sessions to match the unified training data format
 * @param trainingList - Array of completed training sessions
 * @param getVesselWithIcon - Function to get complete vessel data with position/icon
 * @returns Array of transformed training data
 */
export const transformCompletedTrainingToUnifiedFormat = (
    trainingList: any[],
    getVesselWithIcon?: (id: any, vessel: any) => any
): UnifiedTrainingData[] => {
    if (!trainingList || !Array.isArray(trainingList)) {
        return []
    }

    console.log('🔄 Transforming completed training data:', {
        count: trainingList.length,
        sample: trainingList[0] || null
    })

    return trainingList.map((training: any) => {
        // Ensure vessel has complete data including position if function is provided
        const completeVesselData = getVesselWithIcon 
            ? getVesselWithIcon(training.vessel?.id, training.vessel)
            : training.vessel || { id: 0, title: 'Unknown' }

        return {
            id: training.id,
            dueDate: training.date, // Map completion date to dueDate for unified sorting
            vesselID: training.vessel?.id || 0,
            vessel: completeVesselData,
            trainingTypeID: training.trainingTypes?.nodes?.[0]?.id || 0,
            trainingType: training.trainingTypes?.nodes?.[0] || { id: 0, title: 'Unknown' },
            members: training.members?.nodes || [],
            status: {
                label: 'Completed',
                isOverdue: false,
                class: 'border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center',
                dueWithinSevenDays: false,
            },
            category: 'completed' as const,
            originalData: training
        }
    })
}

/**
 * Transform training session dues to unified format with calculated status
 * @param trainingSessionDues - Array of training session dues (overdue/upcoming)
 * @returns Array of transformed training data with calculated status
 */
export const transformTrainingSessionDuesToUnifiedFormat = (
    trainingSessionDues: any[]
): UnifiedTrainingData[] => {
    if (!trainingSessionDues || !Array.isArray(trainingSessionDues)) {
        return []
    }

    console.log('🔄 Transforming training session dues:', {
        count: trainingSessionDues.length,
        sample: trainingSessionDues[0] || null
    })

    return trainingSessionDues.map((due: any) => {
        const status = GetTrainingSessionStatus(due)
        
        // Determine category based on status
        let category: 'overdue' | 'upcoming' | 'completed'
        if (status.isOverdue) {
            category = 'overdue'
        } else if (status.dueWithinSevenDays) {
            category = 'upcoming'
        } else {
            category = 'upcoming' // Default for future due dates
        }

        return {
            id: due.id,
            dueDate: due.dueDate,
            vesselID: due.vesselID,
            vessel: due.vessel || { id: 0, title: 'Unknown' },
            trainingTypeID: due.trainingTypeID,
            trainingType: due.trainingType || { id: 0, title: 'Unknown' },
            members: Array.isArray(due.members) ? due.members : [due.member].filter(Boolean),
            status,
            category,
            originalData: due
        }
    })
}

/**
 * Get priority value for sorting based on training category and status
 * @param training - Unified training data item
 * @returns Priority number (lower = higher priority)
 */
const getTrainingPriority = (training: UnifiedTrainingData): number => {
    switch (training.category) {
        case 'overdue':
            return TrainingPriority.OVERDUE
        case 'upcoming':
            return TrainingPriority.UPCOMING
        case 'completed':
            return TrainingPriority.COMPLETED
        default:
            return TrainingPriority.COMPLETED
    }
}

/**
 * Sort unified training data with priority-based ordering
 * @param data - Array of unified training data
 * @returns Sorted array with overdue first, then upcoming, then completed
 */
export const sortUnifiedTrainingData = (data: UnifiedTrainingData[]): UnifiedTrainingData[] => {
    return data.sort((a, b) => {
        // First sort by priority (overdue > upcoming > completed)
        const priorityDiff = getTrainingPriority(a) - getTrainingPriority(b)
        if (priorityDiff !== 0) {
            return priorityDiff
        }

        // Within same priority, sort by date
        const dateA = new Date(a.dueDate).getTime()
        const dateB = new Date(b.dueDate).getTime()

        if (a.category === 'overdue') {
            // For overdue: most overdue first (earliest due date first)
            return dateA - dateB
        } else if (a.category === 'upcoming') {
            // For upcoming: soonest due date first
            return dateA - dateB
        } else {
            // For completed: most recent completion first (latest date first)
            return dateB - dateA
        }
    })
}

/**
 * Main function to merge and sort crew training data from multiple sources
 * @param options - Configuration object with data sources and utilities
 * @returns Unified and sorted training data array
 */
export const mergeAndSortCrewTrainingData = ({
    trainingSessionDues = [],
    completedTrainingList = [],
    getVesselWithIcon,
    includeCompleted = true
}: {
    trainingSessionDues?: any[]
    completedTrainingList?: any[]
    getVesselWithIcon?: (id: any, vessel: any) => any
    includeCompleted?: boolean
}): UnifiedTrainingData[] => {
    try {
        console.log('🚀 Starting crew training data merge:', {
            trainingSessionDuesCount: trainingSessionDues?.length || 0,
            completedTrainingListCount: completedTrainingList?.length || 0,
            includeCompleted
        })

        // Transform overdue/upcoming training data
        const transformedDues = transformTrainingSessionDuesToUnifiedFormat(trainingSessionDues)

        // Transform completed training data if requested
        const transformedCompleted = includeCompleted
            ? transformCompletedTrainingToUnifiedFormat(completedTrainingList, getVesselWithIcon)
            : []

        // Combine all data
        const combinedData = [...transformedDues, ...transformedCompleted]

        console.log('📊 Data transformation complete:', {
            transformedDues: transformedDues.length,
            transformedCompleted: transformedCompleted.length,
            totalCombined: combinedData.length
        })

        // Sort with priority-based ordering
        const sortedData = sortUnifiedTrainingData(combinedData)

        console.log('✅ Final sorted data:', {
            total: sortedData.length,
            categories: sortedData.reduce((acc, item) => {
                acc[item.category] = (acc[item.category] || 0) + 1
                return acc
            }, {} as Record<string, number>)
        })

        return sortedData
    } catch (error) {
        console.error('❌ Error merging and sorting crew training data:', error)
        return []
    }
}

/**
 * Filter unified training data by category
 * @param data - Unified training data array
 * @param categories - Categories to include
 * @returns Filtered data array
 */
export const filterTrainingDataByCategory = (
    data: UnifiedTrainingData[],
    categories: Array<'overdue' | 'upcoming' | 'completed'>
): UnifiedTrainingData[] => {
    return data.filter(item => categories.includes(item.category))
}

/**
 * Get training data statistics
 * @param data - Unified training data array
 * @returns Statistics object with counts by category
 */
export const getTrainingDataStats = (data: UnifiedTrainingData[]) => {
    return {
        total: data.length,
        overdue: data.filter(item => item.category === 'overdue').length,
        upcoming: data.filter(item => item.category === 'upcoming').length,
        completed: data.filter(item => item.category === 'completed').length
    }
}
