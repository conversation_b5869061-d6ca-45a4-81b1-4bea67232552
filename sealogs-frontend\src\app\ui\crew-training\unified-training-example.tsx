'use client'

import { useState, useEffect } from 'react'
import { useLazyQuery } from '@apollo/client'
import { TRAINING_SESSIONS, READ_TRAINING_SESSION_DUES } from '@/app/lib/graphQL/query'
import { UnifiedTrainingTable } from './unified-training-table'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'
import { Card } from '@/components/ui'
import { H2 } from '@/components/ui/typography'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { getTrainingDataStats } from '@/app/lib/crew-training-utils'
import { mergeAndSortCrewTrainingData } from '@/app/lib/crew-training-utils'

interface UnifiedTrainingExampleProps {
    vesselId?: number
    memberId?: number
    isVesselView?: boolean
}

export const UnifiedTrainingExample = ({
    vesselId,
    memberId,
    isVesselView = false,
}: UnifiedTrainingExampleProps) => {
    const [trainingSessionDues, setTrainingSessionDues] = useState<any[]>([])
    const [completedTrainingList, setCompletedTrainingList] = useState<any[]>([])
    const [includeCompleted, setIncludeCompleted] = useState(true)
    const [loading, setLoading] = useState(false)

    const { getVesselWithIcon } = useVesselIconData()

    // Query for training session dues (overdue/upcoming)
    const [queryTrainingSessionDues, { loading: duesLoading }] = useLazyQuery(
        READ_TRAINING_SESSION_DUES,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTrainingSessionDues.nodes || []
                setTrainingSessionDues(data)
            },
            onError: (error: any) => {
                console.error('Error loading training session dues:', error)
            },
        }
    )

    // Query for completed training sessions
    const [queryCompletedTraining, { loading: completedLoading }] = useLazyQuery(
        TRAINING_SESSIONS,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTrainingSessions.nodes || []
                setCompletedTrainingList(data)
            },
            onError: (error: any) => {
                console.error('Error loading completed training:', error)
            },
        }
    )

    // Load data on component mount
    useEffect(() => {
        loadData()
    }, [vesselId, memberId])

    const loadData = async () => {
        setLoading(true)
        
        // Build filters based on props
        const duesFilter: any = {}
        const completedFilter: any = {}

        if (vesselId) {
            duesFilter.vesselID = { eq: vesselId }
            completedFilter.vesselID = { eq: vesselId }
        }

        if (memberId) {
            duesFilter.memberID = { eq: memberId }
            // For completed training, we'd need to filter by members in the training session
        }

        // Load training session dues
        await queryTrainingSessionDues({
            variables: {
                limit: 1000,
                offset: 0,
                filter: duesFilter,
            },
        })

        // Load completed training sessions
        await queryCompletedTraining({
            variables: {
                limit: 1000,
                offset: 0,
                filter: completedFilter,
            },
        })

        setLoading(false)
    }

    // Calculate statistics using the utility function
    const unifiedData = mergeAndSortCrewTrainingData({
        trainingSessionDues,
        completedTrainingList,
        getVesselWithIcon,
        includeCompleted,
    })

    const stats = getTrainingDataStats(unifiedData)

    const isLoading = loading || duesLoading || completedLoading

    return (
        <Card className="p-6">
            <div className="space-y-6">
                {/* Header with statistics */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                        <H2>Crew Training Overview</H2>
                        <p className="text-sm text-muted-foreground mt-1">
                            Unified view of all training activities
                        </p>
                    </div>
                    
                    <div className="flex flex-wrap gap-2">
                        <Badge variant="destructive" type='normal' className="flex items-center gap-1">
                            <span className="w-2 h-2 bg-current rounded-full"></span>
                            Overdue: {stats.overdue}
                        </Badge>
                        <Badge variant="secondary" type='normal' className="flex items-center gap-1">
                            <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                            Upcoming: {stats.upcoming}
                        </Badge>
                        <Badge variant="outline" type='normal' className="flex items-center gap-1">
                            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                            Completed: {stats.completed}
                        </Badge>
                        <Badge variant="outline" type='normal'>
                            Total: {stats.total}
                        </Badge>
                    </div>
                </div>

                {/* Filter controls */}
                <div className="flex flex-wrap gap-2">
                    <Button
                        size="sm"
                        onClick={() => setIncludeCompleted(!includeCompleted)}
                    >
                        {includeCompleted ? "Hide" : "Show"} Completed
                    </Button>
                    
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={loadData}
                        disabled={isLoading}
                    >
                        {isLoading ? "Loading..." : "Refresh"}
                    </Button>
                </div>

                {/* Unified training table */}
                <div className="border rounded-lg">
                    {isLoading ? (
                        <div className="flex items-center justify-center py-8">
                            <div className="text-center">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                                <p className="text-sm text-muted-foreground">Loading training data...</p>
                            </div>
                        </div>
                    ) : (
                        <UnifiedTrainingTable
                            trainingSessionDues={trainingSessionDues}
                            completedTrainingList={completedTrainingList}
                            getVesselWithIcon={getVesselWithIcon}
                            includeCompleted={includeCompleted}
                            memberId={memberId}
                            isVesselView={isVesselView}
                            showToolbar={false}
                        />
                    )}
                </div>

                {/* Summary information */}
                {!isLoading && unifiedData.length > 0 && (
                    <div className="text-sm text-muted-foreground">
                        <p>
                            Showing {unifiedData.length} training record{unifiedData.length !== 1 ? 's' : ''}.
                            Data is sorted with overdue trainings first, then upcoming, then completed.
                        </p>
                    </div>
                )}
            </div>
        </Card>
    )
}

export default UnifiedTrainingExample
