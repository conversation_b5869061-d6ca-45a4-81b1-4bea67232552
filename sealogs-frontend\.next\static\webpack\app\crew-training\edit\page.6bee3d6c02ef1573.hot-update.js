"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/edit/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/type-multiselect-dropdown.tsx":
/*!****************************************************************!*\
  !*** ./src/app/ui/crew-training/type-multiselect-dropdown.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_offline_models_trainingType__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/offline/models/trainingType */ \"(app-pages-browser)/./src/app/offline/models/trainingType.js\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst TrainingTypeMultiSelectDropdown = (param)=>{\n    let { value = [], onChange, locked, offline = false } = param;\n    _s();\n    const [typesOfTraining, setTypesOfTraining] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTrainings, setSelectedTrainings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const limit = 100 // Default limit from the query\n    ;\n    const [currentOffset, setCurrentOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [hasMorePages, setHasMorePages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingMore, setIsLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const trainingTypeModel = new _app_offline_models_trainingType__WEBPACK_IMPORTED_MODULE_3__[\"default\"]();\n    const handleSetTrainingTypes = (data)=>{\n        const formattedData = data.map((trainingType)=>({\n                value: trainingType.id,\n                label: trainingType.title\n            }));\n        formattedData.sort((a, b)=>a.label.localeCompare(b.label));\n        setTypesOfTraining(formattedData);\n        const selectedData = value.map((value)=>{\n            return formattedData.find((type)=>type.value === value);\n        });\n        setSelectedTrainings(selectedData);\n    };\n    const [queryTrainingTypes] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_6__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CREW_TRAINING_TYPES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingTypes.nodes;\n            const pageInfo = response.readTrainingTypes.pageInfo;\n            if (data) {\n                const formattedData = data.map((trainingType)=>({\n                        value: trainingType.id,\n                        label: trainingType.title\n                    }));\n                const updatedTrainingTypes = [\n                    ...typesOfTraining,\n                    ...formattedData\n                ];\n                setTypesOfTraining(updatedTrainingTypes);\n                if (pageInfo.hasNextPage) {\n                    // Prepare for next page\n                    const nextOffset = currentOffset + limit;\n                    setCurrentOffset(nextOffset);\n                    // Load next page\n                    setIsLoadingMore(true);\n                    queryTrainingTypes({\n                        variables: {\n                            limit: limit,\n                            offset: nextOffset\n                        }\n                    });\n                } else {\n                    setHasMorePages(false);\n                    setIsLoadingMore(false);\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingTypes error\", error);\n            setIsLoadingMore(false);\n        }\n    });\n    const loadTrainingTypes = async ()=>{\n        setTypesOfTraining([]);\n        setCurrentOffset(0);\n        setHasMorePages(true);\n        setIsLoadingMore(true);\n        await queryTrainingTypes({\n            variables: {\n                limit: limit,\n                offset: 0\n            }\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoadingMore) {\n            let formattedData = [\n                ...typesOfTraining\n            ];\n            formattedData.sort((a, b)=>a.label.localeCompare(b.label));\n            setTypesOfTraining(formattedData);\n            const selectedData = value.map((value)=>{\n                return formattedData.find((type)=>type.value === value);\n            });\n            setSelectedTrainings(selectedData);\n        }\n    }, [\n        isLoadingMore\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            trainingTypeModel.getAll().then((data)=>{\n                handleSetTrainingTypes(data);\n            });\n        }\n    }, [\n        offline\n    ]);\n    const handleOnChange = (value)=>{\n        // Handle case when all options are deselected (value is null or empty array)\n        if (!value || Array.isArray(value) && value.length === 0) {\n            setSelectedTrainings([]);\n            onChange([]);\n            return;\n        }\n        const selectedValues = Array.isArray(value) ? value : [\n            value\n        ];\n        setSelectedTrainings(selectedValues);\n        onChange(selectedValues);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(value) && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(typesOfTraining)) {\n            const selectedData = value.map((value)=>{\n                return typesOfTraining.find((type)=>type.value === value);\n            });\n            setSelectedTrainings(selectedData);\n        }\n    }, [\n        value,\n        typesOfTraining\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!offline) {\n            loadTrainingTypes();\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_4__.Combobox, {\n        disabled: locked,\n        value: selectedTrainings,\n        options: typesOfTraining,\n        onChange: handleOnChange,\n        isLoading: !typesOfTraining,\n        buttonClassName: \"w-full\",\n        responsiveBadges: true,\n        labelClassName: \"w-full\",\n        placeholder: \"Select training type\",\n        multi: true\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\type-multiselect-dropdown.tsx\",\n        lineNumber: 139,\n        columnNumber: 9\n    }, undefined);\n};\n_s(TrainingTypeMultiSelectDropdown, \"57wkxefNIEgvwFK79REOZDkCI/I=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_6__.useLazyQuery\n    ];\n});\n_c = TrainingTypeMultiSelectDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TrainingTypeMultiSelectDropdown);\nvar _c;\n$RefreshReg$(_c, \"TrainingTypeMultiSelectDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/type-multiselect-dropdown.tsx\n"));

/***/ })

});